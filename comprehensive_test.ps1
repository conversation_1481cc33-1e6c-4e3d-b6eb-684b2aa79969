# Comprehensive Backend Functionality Test
Write-Host "🚀 Starting Comprehensive Backend Test..." -ForegroundColor Green

$baseUrl = "http://localhost:8000"
$testResults = @()

function Test-Endpoint {
    param($name, $method, $url, $headers = @{}, $body = $null, $contentType = $null)
    
    try {
        $params = @{
            Uri = $url
            Method = $method
            Headers = $headers
        }
        
        if ($body) { $params.Body = $body }
        if ($contentType) { $params.ContentType = $contentType }
        
        $response = Invoke-WebRequest @params
        Write-Host "✅ $name - Status: $($response.StatusCode)" -ForegroundColor Green
        return @{ Name = $name; Status = "PASS"; StatusCode = $response.StatusCode; Response = $response.Content }
    }
    catch {
        Write-Host "❌ $name - Error: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Name = $name; Status = "FAIL"; Error = $_.Exception.Message }
    }
}

# Test 1: Health Check
Write-Host "`n📋 Testing Health Endpoints..." -ForegroundColor Yellow
$testResults += Test-Endpoint "Health Check" "GET" "$baseUrl/health"
$testResults += Test-Endpoint "Root Endpoint" "GET" "$baseUrl/"

# Test 2: Authentication
Write-Host "`n🔐 Testing Authentication..." -ForegroundColor Yellow

# Register new user
$registerBody = "username=testuser$(Get-Random)@example.com&password=testpass123"
$testResults += Test-Endpoint "User Registration" "POST" "$baseUrl/api/v1/auth/register" @{} $registerBody "application/x-www-form-urlencoded"

# Login
$loginBody = "username=<EMAIL>&password=testpassword123"
$loginResult = Test-Endpoint "User Login" "POST" "$baseUrl/api/v1/auth/login" @{} $loginBody "application/x-www-form-urlencoded"
$testResults += $loginResult

if ($loginResult.Status -eq "PASS") {
    $tokenData = $loginResult.Response | ConvertFrom-Json
    $token = $tokenData.access_token
    $authHeaders = @{"Authorization" = "Bearer $token"}
    
    # Test 3: Document Management
    Write-Host "`n📄 Testing Document Management..." -ForegroundColor Yellow
    
    # List documents
    $testResults += Test-Endpoint "List Documents" "GET" "$baseUrl/api/v1/documents/" $authHeaders
    
    # Test 4: Analytics
    Write-Host "`n📊 Testing Analytics..." -ForegroundColor Yellow
    $testResults += Test-Endpoint "Usage Analytics" "GET" "$baseUrl/api/v1/analytics/usage" $authHeaders
    $testResults += Test-Endpoint "Trends Analytics" "GET" "$baseUrl/api/v1/analytics/trends" $authHeaders
    
    # Test 5: Processing
    Write-Host "`n⚙️ Testing Processing..." -ForegroundColor Yellow
    $testResults += Test-Endpoint "Processing Status" "GET" "$baseUrl/api/v1/processing/1/status" $authHeaders
    
    Write-Host "`n🎯 All tests completed!" -ForegroundColor Green
} else {
    Write-Host "❌ Cannot proceed with authenticated tests - login failed" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 TEST SUMMARY:" -ForegroundColor Cyan
$passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalCount = $testResults.Count

Write-Host "Total Tests: $totalCount" -ForegroundColor White
Write-Host "Passed: $passCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passCount / $totalCount) * 100, 2))%" -ForegroundColor Cyan

# Detailed Results
Write-Host "`n📋 DETAILED RESULTS:" -ForegroundColor Cyan
foreach ($result in $testResults) {
    $status = if ($result.Status -eq "PASS") { "✅" } else { "❌" }
    Write-Host "$status $($result.Name)" -ForegroundColor White
    if ($result.Status -eq "FAIL") {
        Write-Host "   Error: $($result.Error)" -ForegroundColor Red
    }
}
