#!/bin/bash

# Deployment script for production

# Check if running in production
if [ "$ENVIRONMENT" != "production" ]; then
    echo "This script should only be run in production!"
    exit 1
fi

# Pull latest changes
git pull origin main

# Build and start containers
docker-compose -f docker-compose.prod.yml up --build -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Clean up old images
docker image prune -f

echo "Deployment completed successfully!"
