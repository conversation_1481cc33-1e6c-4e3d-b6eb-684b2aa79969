from celery import Celery
import pytesseract
from PIL import Image
import os

from app.database import <PERSON>Local
from app.models.document import Document, DocumentStatus

celery = Celery('tasks', broker='redis://redis:6379/0')

@celery.task
def process_ocr(document_id: int):
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return {"error": "Document not found"}
        
        document.status = DocumentStatus.PROCESSING
        db.commit()
        
        # Perform OCR
        image = Image.open(document.file_path)
        text = pytesseract.image_to_string(image)
        
        document.processed_text = text
        document.status = DocumentStatus.COMPLETED
        db.commit()
        
        return {"success": True, "document_id": document_id}
    except Exception as e:
        document.status = DocumentStatus.FAILED
        db.commit()
        return {"error": str(e)}
    finally:
        db.close()


def extract_text_from_document(file_path: str) -> str:
    """Extract text from document using OCR."""
    try:
        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            return text
        elif file_path.lower().endswith('.pdf'):
            # For PDF files, we would need pdf2image
            # For now, return placeholder
            return "PDF text extraction not implemented yet"
        elif file_path.lower().endswith('.txt'):
            # Handle text files directly
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            return "Unsupported file format"
    except Exception as e:
        return f"Error extracting text: {str(e)}"
