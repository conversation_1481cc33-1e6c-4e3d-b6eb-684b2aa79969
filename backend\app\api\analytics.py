from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta

from app.database import get_db
from app.models.document import Document
from app.services.auth_service import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("/usage")
async def get_usage_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Get total documents
    total_docs = db.query(func.count(Document.id))\
        .filter(Document.user_id == current_user.id)\
        .scalar()
    
    # Get documents processed in last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_docs = db.query(func.count(Document.id))\
        .filter(
            Document.user_id == current_user.id,
            Document.created_at >= thirty_days_ago
        )\
        .scalar()
    
    # Get processing status breakdown
    status_breakdown = db.query(
        Document.status,
        func.count(Document.id)
    )\
        .filter(Document.user_id == current_user.id)\
        .group_by(Document.status)\
        .all()
    
    return {
        "total_documents": total_docs,
        "recent_documents": recent_docs,
        "status_breakdown": dict(status_breakdown)
    }

@router.get("/trends")
async def get_usage_trends(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Get daily document counts for last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    daily_counts = db.query(
        func.date_trunc('day', Document.created_at).label('date'),
        func.count(Document.id)
    )\
        .filter(
            Document.user_id == current_user.id,
            Document.created_at >= thirty_days_ago
        )\
        .group_by('date')\
        .order_by('date')\
        .all()
    
    return {
        "daily_trends": [
            {"date": date.strftime("%Y-%m-%d"), "count": count}
            for date, count in daily_counts
        ]
    }
