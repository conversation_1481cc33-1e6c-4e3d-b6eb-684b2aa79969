# Document Processing Platform

A comprehensive, modern platform for document processing, OCR, machine learning classification, and analytics built with FastAPI and React.

## 🚀 Features

### Core Functionality
- **Document Upload & Management** - Support for PDF, images (PNG, JPEG, TIFF), and text files
- **OCR Processing** - Extract text from images and scanned documents using Tesseract
- **ML Classification** - Automatically classify documents (invoices, contracts, receipts, forms)
- **Image Enhancement** - Preprocessing for better OCR accuracy
- **PDF Generation** - Create searchable PDFs from processed documents
- **Real-time Processing** - Background task processing with Celery and Redis
- **Analytics Dashboard** - Comprehensive insights and reporting

### Technical Features
- **RESTful API** - Well-documented FastAPI backend with automatic OpenAPI docs
- **Real-time Updates** - WebSocket support for live processing status
- **User Authentication** - JWT-based authentication with role-based access
- **Caching** - Redis-based caching for improved performance
- **Database** - PostgreSQL with Alembic migrations
- **Containerized** - Full Docker support for development and production
- **Health Monitoring** - Built-in health checks and monitoring endpoints

## 🛠 Tech Stack

### Backend
- **FastAPI** - Modern, fast web framework for building APIs
- **PostgreSQL** - Robust relational database
- **Redis** - In-memory data store for caching and task queues
- **Celery** - Distributed task queue for background processing
- **SQLAlchemy** - Python SQL toolkit and ORM
- **Alembic** - Database migration tool
- **Tesseract OCR** - Optical character recognition engine
- **Scikit-learn** - Machine learning library for document classification

### Frontend
- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe JavaScript development
- **Redux Toolkit** - State management
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client for API communication

### Infrastructure
- **Docker & Docker Compose** - Containerization and orchestration
- **Nginx** - Reverse proxy and static file serving
- **GitHub Actions** - CI/CD pipeline (planned)

## 🏗 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React SPA     │    │   FastAPI       │    │   PostgreSQL    │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │     Redis       │    │     Celery      │
                       │   (Cache/Queue) │◄──►│   (Workers)     │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- **Docker** and **Docker Compose** (recommended)
- **Git** for version control

### Option 1: Docker Setup (Recommended)

1. **Clone the repository**
```bash
git clone <repository-url>
cd document-processing-platform
```

2. **Run the setup script**
```bash
# Linux/macOS
chmod +x scripts/setup.sh
./scripts/setup.sh

# Windows (PowerShell)
.\scripts\setup.ps1
```

3. **Start the application**
```bash
docker-compose up
```

4. **Access the application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Admin Interface: http://localhost:8000/redoc

### Option 2: Local Development Setup

1. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Frontend Setup**
```bash
cd frontend
npm install
```

3. **Database Setup**
```bash
# Start PostgreSQL and Redis
docker-compose up -d db redis

# Run migrations
cd backend
alembic upgrade head
```

4. **Start Services**
```bash
# Terminal 1: Backend
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Frontend
cd frontend
npm start

# Terminal 3: Celery Worker (optional)
cd backend
celery -A app.celery_app worker --loglevel=info
```

## 📁 Project Structure

```
document-processing-platform/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   ├── models/            # Database models
│   │   ├── services/          # Business logic
│   │   ├── utils/             # Utility functions
│   │   └── workers/           # Celery workers
│   ├── alembic/               # Database migrations
│   └── requirements.txt       # Python dependencies
├── frontend/                   # React frontend
│   ├── src/
│   │   ├── components/        # React components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API services
│   │   ├── hooks/             # Custom hooks
│   │   └── store/             # Redux store
│   └── package.json           # Node.js dependencies
├── nginx/                      # Nginx configuration
├── scripts/                    # Setup and deployment scripts
├── docs/                       # Documentation
├── docker-compose.yml          # Docker services
└── .env.example               # Environment variables template
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/document_processing

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# File Upload
UPLOAD_FOLDER=uploads
MAX_FILE_SIZE=10485760  # 10MB

# OCR Settings
TESSERACT_CMD=/usr/bin/tesseract  # Optional: custom Tesseract path
OCR_LANGUAGES=["eng"]             # Supported languages
```

## 📚 API Documentation

The API is automatically documented using FastAPI's built-in OpenAPI support:

- **Interactive Docs**: http://localhost:8000/docs (Swagger UI)
- **Alternative Docs**: http://localhost:8000/redoc (ReDoc)
- **OpenAPI Schema**: http://localhost:8000/openapi.json

### Key Endpoints

- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/documents/upload` - Upload documents
- `GET /api/v1/documents` - List documents
- `POST /api/v1/processing/start` - Start document processing
- `GET /api/v1/analytics/dashboard` - Analytics data

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 🚀 Deployment

See [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md) for detailed deployment instructions.

### Production Deployment

```bash
# Build and start production services
docker-compose -f docker-compose.prod.yml up -d

# Or use the deployment script
./scripts/deploy.sh
```

## 📖 Documentation

- [API Documentation](docs/API.md) - Detailed API reference
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment
- [User Guide](docs/USER_GUIDE.md) - End-user documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- Create an issue for bug reports or feature requests
- Check the [documentation](docs/) for common questions
- Review the [API documentation](http://localhost:8000/docs) for API usage

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the excellent web framework
- [React](https://reactjs.org/) for the frontend framework
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) for text extraction
- [PostgreSQL](https://www.postgresql.org/) for the database
- [Redis](https://redis.io/) for caching and task queues
