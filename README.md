# Document Processing Platform

A modern platform for document processing, OCR, and analytics.

## Features

- Document upload and management
- OCR processing
- Image enhancement
- PDF generation
- Analytics dashboard
- User authentication
- API access

## Tech Stack

- Backend: FastAPI (Python)
- Frontend: React with TypeScript
- Database: PostgreSQL
- Processing: Tesseract OCR, OpenCV
- Container: Docker
- CI/CD: GitHub Actions

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js 16+
- Python 3.9+

### Development Setup

1. Clone the repository
```bash
git clone https://github.com/yourusername/document-processing-platform.git
cd document-processing-platform
```

2. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configurations
```

3. Start the development environment
```bash
docker-compose up --build
```

4. Access the applications:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## Documentation

- [API Documentation](docs/API.md)
- [Deployment Guide](docs/DEPLOYMENT.md)
- [User Guide](docs/USER_GUIDE.md)

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
