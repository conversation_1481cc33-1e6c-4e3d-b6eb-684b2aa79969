from celery import Celery
from fpdf import FPDF
import os

from app.database import <PERSON>Local
from app.models.document import Document, DocumentStatus

celery = Celery('tasks', broker='redis://redis:6379/0')

@celery.task
def generate_pdf(document_id: int, content: str):
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return {"error": "Document not found"}
        
        document.status = DocumentStatus.PROCESSING
        db.commit()
        
        # Create PDF
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        
        # Split content into lines and add to PDF
        for line in content.split('\n'):
            pdf.cell(200, 10, txt=line, ln=True, align='L')
        
        # Save PDF
        pdf_path = f"{os.path.splitext(document.file_path)[0]}_generated.pdf"
        pdf.output(pdf_path)
        
        document.file_path = pdf_path
        document.status = DocumentStatus.COMPLETED
        db.commit()
        
        return {"success": True, "document_id": document_id}
    except Exception as e:
        document.status = DocumentStatus.FAILED
        db.commit()
        return {"error": str(e)}
    finally:
        db.close()
