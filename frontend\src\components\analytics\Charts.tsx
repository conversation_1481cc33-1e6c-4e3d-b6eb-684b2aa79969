import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend
} from 'recharts';
import { useAnalytics } from 'hooks/useAnalytics';

export const Charts: React.FC = () => {
  const { usageStats } = useAnalytics();

  const COLORS = ['#0284c7', '#22c55e', '#ef4444', '#f59e0b'];

  const statusData = usageStats?.status_breakdown ? 
    Object.entries(usageStats.status_breakdown).map(([name, value]) => ({
      name,
      value
    })) : [];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Document Status Distribution */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Document Status Distribution
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Add more charts as needed */}
    </div>
  );
};
