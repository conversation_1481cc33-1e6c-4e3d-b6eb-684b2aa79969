from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class DocumentBase(BaseModel):
    name: str
    mime_type: str


class DocumentCreate(DocumentBase):
    pass


class DocumentResponse(DocumentBase):
    id: int
    file_path: str
    status: str
    user_id: int
    processed_text: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True  # This allows Pydantic to work with SQLAlchemy models


class DocumentListResponse(BaseModel):
    documents: list[DocumentResponse]
    total: int
    page: int
    per_page: int


class DocumentUploadResponse(BaseModel):
    id: int
    name: str
    status: str
    message: str
