import React from 'react';
import {
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import { useAnalytics } from 'hooks/useAnalytics';

export const Dashboard: React.FC = () => {
  const { usageStats, trends } = useAnalytics();

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Total Documents</h3>
          <p className="mt-2 text-3xl font-semibold text-primary-600">
            {usageStats?.total_documents || 0}
          </p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Recent (30 days)</h3>
          <p className="mt-2 text-3xl font-semibold text-primary-600">
            {usageStats?.recent_documents || 0}
          </p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Processing Success Rate</h3>
          <p className="mt-2 text-3xl font-semibold text-primary-600">
            {usageStats?.success_rate || 0}%
          </p>
        </div>
      </div>

      {/* Usage Trends Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Usage Trends</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="count"
                stroke="#0284c7"
                name="Documents Processed"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};
