-- Initialize Document Processing Platform Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
CREATE TYPE document_status AS ENUM ('pending', 'processing', 'completed', 'failed');
CREATE TYPE document_type AS ENUM ('invoice', 'contract', 'receipt', 'form', 'other');
CREATE TYPE user_role AS ENUM ('admin', 'user', 'viewer');

-- Create indexes for better performance
-- These will be created by Alembic migrations, but we can prepare the database

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE document_processing TO postgres;
