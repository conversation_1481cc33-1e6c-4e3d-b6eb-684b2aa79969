from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Enum
from sqlalchemy.sql import func
from app.database import Base
import enum

class DocumentStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    file_path = Column(String)
    mime_type = Column(String)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.PENDING)
    user_id = Column(Integer, ForeignKey("users.id"))
    processed_text = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
