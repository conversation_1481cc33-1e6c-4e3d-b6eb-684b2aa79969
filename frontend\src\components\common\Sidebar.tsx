import React from 'react';
import { Link, useLocation } from 'react-router-dom';

export const Sidebar: React.FC = () => {
  const location = useLocation();

  const navigation = [
    { name: 'Dashboard', href: '/', icon: 'home' },
    { name: 'Documents', href: '/documents', icon: 'document' },
    { name: 'Processing', href: '/processing', icon: 'cog' },
    { name: 'Analytics', href: '/analytics', icon: 'chart-bar' },
  ];

  return (
    <aside className="bg-gray-800 w-64 min-h-screen p-4">
      <div className="flex flex-col h-full">
        <div className="space-y-4">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`
                flex items-center px-4 py-2 text-sm rounded-md
                ${location.pathname === item.href
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }
              `}
            >
              <span className="material-icons mr-3">{item.icon}</span>
              {item.name}
            </Link>
          ))}
        </div>
      </div>
    </aside>
  );
};
