import React from 'react';
import { useDocuments } from 'hooks/useDocuments';
import { Document } from 'types/document';

export const DocumentList: React.FC = () => {
  const { documents, deleteDocument } = useDocuments();

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      await deleteDocument(id);
    }
  };

  return (
    <div className="overflow-x-auto relative">
      <table className="w-full text-sm text-left text-gray-500">
        <thead className="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" className="py-3 px-6">Name</th>
            <th scope="col" className="py-3 px-6">Status</th>
            <th scope="col" className="py-3 px-6">Created At</th>
            <th scope="col" className="py-3 px-6">Actions</th>
          </tr>
        </thead>
        <tbody>
          {documents.map((doc: Document) => (
            <tr key={doc.id} className="bg-white border-b hover:bg-gray-50">
              <td className="py-4 px-6">{doc.name}</td>
              <td className="py-4 px-6">
                <span className={`px-2 py-1 rounded text-xs ${
                  doc.status === 'completed' ? 'bg-green-100 text-green-800' :
                  doc.status === 'failed' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {doc.status}
                </span>
              </td>
              <td className="py-4 px-6">
                {new Date(doc.created_at).toLocaleDateString()}
              </td>
              <td className="py-4 px-6">
                <button
                  onClick={() => handleDelete(doc.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
