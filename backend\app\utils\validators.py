from typing import Dict, Any
from pydantic import BaseModel, EmailStr, validator
import re

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    
    @validator('password')
    def password_strength(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one number')
        return v

class DocumentBase(BaseModel):
    name: str
    
    @validator('name')
    def name_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Document name cannot be empty')
        return v

def validate_document_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate document input data."""
    errors = {}
    
    if 'name' not in data or not data['name'].strip():
        errors['name'] = 'Document name is required'
    
    if errors:
        raise ValueError(errors)
    
    return data
