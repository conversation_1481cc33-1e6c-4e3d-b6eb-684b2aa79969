import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocuments } from 'hooks/useDocuments';

export const UploadZone: React.FC = () => {
  const { uploadDocument } = useDocuments();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      await uploadDocument(file);
    }
  }, [uploadDocument]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg', '.tiff']
    },
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  return (
    <div
      {...getRootProps()}
      className={`
        p-10 border-2 border-dashed rounded-lg text-center cursor-pointer
        ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'}
      `}
    >
      <input {...getInputProps()} />
      {isDragActive ? (
        <p className="text-primary-600">Drop the files here...</p>
      ) : (
        <div>
          <p className="text-gray-600">Drag and drop files here, or click to select files</p>
          <p className="text-sm text-gray-500 mt-2">
            Supported formats: PDF, PNG, JPG, TIFF (max 10MB)
          </p>
        </div>
      )}
    </div>
  );
};
