import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Document {
  id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path: string;
  mime_type?: string;
  created_at: string;
}

interface DocumentsState {
  documents: Document[];
  loading: boolean;
  error: string | null;
  selectedDocument: Document | null;
}

const initialState: DocumentsState = {
  documents: [],
  loading: false,
  error: null,
  selectedDocument: null,
};

const documentsSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setDocuments: (state, action: PayloadAction<Document[]>) => {
      state.documents = action.payload;
    },
    addDocument: (state, action: PayloadAction<Document>) => {
      state.documents.push(action.payload);
    },
    removeDocument: (state, action: PayloadAction<number>) => {
      state.documents = state.documents.filter(doc => doc.id !== action.payload);
    },
    updateDocument: (state, action: PayloadAction<Document>) => {
      const index = state.documents.findIndex(doc => doc.id === action.payload.id);
      if (index !== -1) {
        state.documents[index] = action.payload;
      }
    },
    setSelectedDocument: (state, action: PayloadAction<Document | null>) => {
      state.selectedDocument = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setDocuments,
  addDocument,
  removeDocument,
  updateDocument,
  setSelectedDocument,
  setLoading,
  setError,
} = documentsSlice.actions;

export default documentsSlice.reducer;
