#!/bin/bash

# Setup script for Document Processing Platform development environment

set -e  # Exit on any error

echo "🚀 Setting up Document Processing Platform..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "📝 Please review and update the .env file with your specific configuration"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p uploads
mkdir -p backend/logs
mkdir -p backend/models
mkdir -p data/postgres
mkdir -p data/redis

# Set proper permissions
chmod 755 uploads
chmod 755 backend/logs
chmod 755 backend/models

echo "🐳 Starting Docker services..."
docker-compose up -d db redis

echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Docker services are running"
else
    echo "❌ Failed to start Docker services"
    exit 1
fi

echo "📦 Building application containers..."
docker-compose build

echo "🔧 Setting up database..."
# Run database migrations
docker-compose run --rm backend alembic upgrade head

echo "🎉 Setup completed successfully!"
echo ""
echo "To start the development environment:"
echo "  docker-compose up"
echo ""
echo "To start in background:"
echo "  docker-compose up -d"
echo ""
echo "To view logs:"
echo "  docker-compose logs -f"
echo ""
echo "To stop services:"
echo "  docker-compose down"
echo ""
echo "Access points:"
echo "  - Frontend: http://localhost:3000"
echo "  - Backend API: http://localhost:8000"
echo "  - API Documentation: http://localhost:8000/docs"
echo "  - PostgreSQL: localhost:5432"
echo "  - Redis: localhost:6379"
