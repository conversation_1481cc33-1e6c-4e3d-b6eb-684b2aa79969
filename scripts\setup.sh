#!/bin/bash

# Setup script for development environment

# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
cd ../frontend
npm install

# Create .env file if it doesn't exist
cd ..
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Created .env file from .env.example"
fi

# Create necessary directories
mkdir -p backend/uploads
mkdir -p backend/logs

echo "Setup completed successfully!"
