from celery import Celery
from app.services.ocr_service import process_ocr
from app.services.image_service import enhance_image
from app.services.ml_service import classify_document
from app.database import SessionLocal
from app.models.document import Document, DocumentStatus

celery_app = Celery(
    'document_processing',
    broker='redis://redis:6379/0',
    backend='redis://redis:6379/1'
)

@celery_app.task(name='process_document')
def process_document_task(document_id: int):
    """
    Main task for document processing pipeline.
    This task coordinates the entire processing workflow.
    """
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return {"error": "Document not found"}
        
        # Update status to processing
        document.status = DocumentStatus.PROCESSING
        db.commit()
        
        # Step 1: Image Enhancement
        enhance_result = enhance_image.delay(document_id)
        enhance_result.get()  # Wait for completion
        
        # Step 2: OCR Processing
        ocr_result = process_ocr.delay(document_id)
        ocr_result.get()  # Wait for completion
        
        # Step 3: Document Classification
        classify_result = classify_document.delay(document_id)
        classification = classify_result.get()
        
        # Update document status
        document.status = DocumentStatus.COMPLETED
        db.commit()
        
        return {
            "success": True,
            "document_id": document_id,
            "classification": classification.get('classification', 'unknown')
        }
    except Exception as e:
        if document:
            document.status = DocumentStatus.FAILED
            db.commit()
        return {"error": str(e)}
    finally:
        db.close()
