import os
import uuid
from typing import <PERSON><PERSON>
from fastapi import UploadFile
import magic
from app.config import settings

def is_valid_file(file: UploadFile) -> Tuple[bool, str]:
    """
    Validate file type and size.
    Returns: (is_valid, error_message)
    """
    # Check file extension
    ext = os.path.splitext(file.filename)[1][1:].lower()
    if ext not in settings.ALLOWED_EXTENSIONS:
        return False, f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}"
    
    # Check file size
    try:
        file.file.seek(0, os.SEEK_END)
        size = file.file.tell()
        file.file.seek(0)
        
        if size > settings.MAX_FILE_SIZE:
            return False, f"File too large. Maximum size: {settings.MAX_FILE_SIZE / 1024 / 1024}MB"
    except Exception as e:
        return False, f"Error checking file size: {str(e)}"
    
    return True, ""

def generate_unique_filename(original_filename: str) -> str:
    """Generate a unique filename while preserving the original extension."""
    ext = os.path.splitext(original_filename)[1]
    return f"{uuid.uuid4()}{ext}"

def get_file_mime_type(file_path: str) -> str:
    """Get the MIME type of a file."""
    return magic.from_file(file_path, mime=True)

def ensure_upload_directory():
    """Ensure the upload directory exists."""
    os.makedirs(settings.UPLOAD_FOLDER, exist_ok=True)
