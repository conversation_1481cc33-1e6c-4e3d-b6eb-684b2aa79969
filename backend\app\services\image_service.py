from celery import Celery
import cv2
import numpy as np
from PIL import Image
import os

from app.database import SessionLocal
from app.models.document import Document, DocumentStatus

celery = Celery('tasks', broker='redis://redis:6379/0')

@celery.task
def enhance_image(document_id: int):
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return {"error": "Document not found"}
        
        document.status = DocumentStatus.PROCESSING
        db.commit()
        
        # Load image
        image = cv2.imread(document.file_path)
        
        # Basic image enhancement
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply adaptive thresholding
        enhanced = cv2.adaptiveThreshold(
            gray, 255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Denoise
        enhanced = cv2.fastNlMeansDenoising(enhanced)
        
        # Save enhanced image
        enhanced_path = f"{os.path.splitext(document.file_path)[0]}_enhanced.png"
        cv2.imwrite(enhanced_path, enhanced)
        
        document.file_path = enhanced_path
        document.status = DocumentStatus.COMPLETED
        db.commit()
        
        return {"success": True, "document_id": document_id}
    except Exception as e:
        document.status = DocumentStatus.FAILED
        db.commit()
        return {"error": str(e)}
    finally:
        db.close()
