from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.document import Document
from app.services.auth_service import get_current_user
from app.services.ocr_service import process_ocr
from app.services.image_service import enhance_image
from app.models.user import User

router = APIRouter()

@router.post("/{document_id}/ocr")
async def start_ocr(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Start OCR processing
    process_ocr.delay(document.id)
    return {"message": "OCR processing started"}

@router.post("/{document_id}/enhance")
async def enhance_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Start image enhancement
    enhance_image.delay(document.id)
    return {"message": "Image enhancement started"}

@router.get("/{document_id}/status")
async def get_processing_status(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return {"status": document.status}
