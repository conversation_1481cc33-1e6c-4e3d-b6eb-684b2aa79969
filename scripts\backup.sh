#!/bin/bash

# Backup script for database and uploaded files

# Set backup directory
BACKUP_DIR="/var/backups/document-processing"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Backup database
docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U postgres document_processing > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql"

# Backup uploaded files
tar -czf "$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz" backend/uploads/

# Keep only last 7 days of backups
find "$BACKUP_DIR" -type f -mtime +7 -delete

echo "Backup completed successfully!"
