{"name": "document-processing-platform", "version": "1.0.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@tailwindcss/forms": "^0.5.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.39", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "tailwindcss": "^3.3.3", "postcss": "^8.4.27", "autoprefixer": "^10.4.14"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}