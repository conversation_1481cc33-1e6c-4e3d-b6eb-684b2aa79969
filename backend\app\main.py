from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import os
import logging
from contextlib import asynccontextmanager

from app.api import auth, documents, processing, analytics
from app.config import settings
from app.database import check_db_connection, check_redis_connection

# Configure logging
logging.basicConfig(level=getattr(logging, settings.LOG_LEVEL))
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(_app: FastAPI):
    # Startup
    logger.info("Starting Document Processing Platform API")

    # Create upload directory if it doesn't exist
    os.makedirs(settings.UPLOAD_FOLDER, exist_ok=True)

    # Check database connection
    if not check_db_connection():
        logger.error("Failed to connect to database")
        raise HTTPException(status_code=500, detail="Database connection failed")

    # Check Redis connection
    if not check_redis_connection():
        logger.error("Failed to connect to Redis")
        raise HTTPException(status_code=500, detail="Redis connection failed")

    logger.info("All services connected successfully")
    yield

    # Shutdown
    logger.info("Shutting down Document Processing Platform API")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for uploads
if os.path.exists(settings.UPLOAD_FOLDER):
    app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_FOLDER), name="uploads")

# Include routers
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["authentication"])
app.include_router(documents.router, prefix=f"{settings.API_V1_STR}/documents", tags=["documents"])
app.include_router(processing.router, prefix=f"{settings.API_V1_STR}/processing", tags=["processing"])
app.include_router(analytics.router, prefix=f"{settings.API_V1_STR}/analytics", tags=["analytics"])

@app.get("/")
async def root():
    return {
        "message": "Welcome to Document Processing Platform API",
        "version": settings.VERSION,
        "docs": "/docs" if settings.DEBUG else "Documentation disabled in production",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    db_status = check_db_connection()
    redis_status = check_redis_connection()

    if not db_status or not redis_status:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "database": "connected" if db_status else "disconnected",
                "redis": "connected" if redis_status else "disconnected"
            }
        )

    return {
        "status": "healthy",
        "database": "connected",
        "redis": "connected",
        "version": settings.VERSION
    }
