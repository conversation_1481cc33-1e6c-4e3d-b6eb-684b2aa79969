from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, documents, processing, analytics
from app.config import settings

app = FastAPI(
    title="Document Processing Platform",
    description="API for document processing and management",
    version="1.0.0"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(processing.router, prefix="/api/processing", tags=["processing"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])

@app.get("/")
async def root():
    return {"message": "Welcome to Document Processing Platform API"}
