import React, { useEffect, useState } from 'react';
import { Document } from 'types/document';

interface DocumentViewerProps {
  document: Document;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({ document }) => {
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    const loadPreview = async () => {
      try {
        // Add preview loading logic based on document type
        if (document.mime_type?.startsWith('image/')) {
          setPreview(document.file_path);
        } else if (document.mime_type === 'application/pdf') {
          // Handle PDF preview
          setPreview(`data:application/pdf;base64,${document.file_path}`);
        }
      } catch (error) {
        console.error('Error loading preview:', error);
      }
    };

    loadPreview();
  }, [document]);

  if (!preview) {
    return <div className="text-center p-4">No preview available</div>;
  }

  return (
    <div className="w-full h-full min-h-[500px] border rounded-lg overflow-hidden">
      {document.mime_type?.startsWith('image/') ? (
        <img
          src={preview}
          alt={document.name}
          className="w-full h-full object-contain"
        />
      ) : (
        <iframe
          src={preview}
          title={document.name}
          className="w-full h-full"
        />
      )}
    </div>
  );
};
