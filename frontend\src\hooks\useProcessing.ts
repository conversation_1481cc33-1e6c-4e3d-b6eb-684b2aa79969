import { useState, useCallback } from 'react';
import api from 'services/api';

interface ProcessingStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export const useProcessing = () => {
  const [status, setStatus] = useState<ProcessingStatus | null>(null);
  const [loading, setLoading] = useState(false);

  const startProcessing = useCallback(async (documentId: number) => {
    setLoading(true);
    try {
      await api.post(`/processing/${documentId}/ocr`);
      return true;
    } catch (err) {
      throw new Error('Failed to start processing');
    } finally {
      setLoading(false);
    }
  }, []);

  const checkStatus = useCallback(async (documentId: number) => {
    try {
      const response = await api.get(`/processing/${documentId}/status`);
      setStatus(response.data);
      return response.data;
    } catch (err) {
      throw new Error('Failed to check processing status');
    }
  }, []);

  const enhanceDocument = useCallback(async (documentId: number) => {
    setLoading(true);
    try {
      await api.post(`/processing/${documentId}/enhance`);
      return true;
    } catch (err) {
      throw new Error('Failed to enhance document');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    status,
    loading,
    startProcessing,
    checkStatus,
    enhanceDocument
  };
};
