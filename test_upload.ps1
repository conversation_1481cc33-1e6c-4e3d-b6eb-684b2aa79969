# Test file upload script
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************.vkL_7jnCJkWWtxgckRrgvCsAztq6fA81q8nmxpaRoT4"
$uri = "http://localhost:8000/api/v1/documents/upload"
$filePath = "test_document2.txt"

# Create multipart form data
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

# Read file content
$fileContent = Get-Content $filePath -Raw
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)

# Build multipart body
$bodyLines = @()
$bodyLines += "--$boundary"
$bodyLines += 'Content-Disposition: form-data; name="file"; filename="test_document2.txt"'
$bodyLines += "Content-Type: text/plain"
$bodyLines += ""
$bodyLines += $fileContent
$bodyLines += "--$boundary--"

$body = $bodyLines -join $LF

# Make request
try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers @{"Authorization" = "Bearer $token"} -ContentType "multipart/form-data; boundary=$boundary" -Body $body
    Write-Host "Success! Response:"
    Write-Host $response.Content
} catch {
    Write-Host "Error:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
