from celery import Celery
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from typing import List

from app.database import SessionLocal
from app.models.document import Document

celery = Celery('tasks', broker='redis://redis:6379/0')

class DocumentClassifier:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000)
        self.classifier = MultinomialNB()
        self.labels = ['invoice', 'contract', 'report', 'other']
    
    def train(self, texts: List[str], labels: List[str]):
        X = self.vectorizer.fit_transform(texts)
        self.classifier.fit(X, labels)
    
    def predict(self, text: str) -> str:
        X = self.vectorizer.transform([text])
        return self.classifier.predict(X)[0]

@celery.task
def classify_document(document_id: int):
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document or not document.processed_text:
            return {"error": "Document not found or not processed"}
        
        # Initialize classifier
        classifier = DocumentClassifier()
        
        # In a real application, you would load a pre-trained model
        # For now, we'll return a dummy classification
        document_type = "other"
        
        return {
            "success": True,
            "document_id": document_id,
            "classification": document_type
        }
    except Exception as e:
        return {"error": str(e)}
    finally:
        db.close()


def analyze_document(text: str) -> dict:
    """Analyze document text and return analysis results."""
    try:
        # Simple analysis - count words, sentences, etc.
        words = len(text.split())
        sentences = len([s for s in text.split('.') if s.strip()])

        # Simple classification based on keywords
        classification = "other"
        if any(word in text.lower() for word in ['invoice', 'bill', 'payment']):
            classification = "invoice"
        elif any(word in text.lower() for word in ['contract', 'agreement', 'terms']):
            classification = "contract"
        elif any(word in text.lower() for word in ['report', 'analysis', 'summary']):
            classification = "report"

        return {
            "word_count": words,
            "sentence_count": sentences,
            "classification": classification,
            "confidence": 0.8  # Dummy confidence score
        }
    except Exception as e:
        return {
            "error": str(e),
            "word_count": 0,
            "sentence_count": 0,
            "classification": "unknown",
            "confidence": 0.0
        }
