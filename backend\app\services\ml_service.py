from celery import Celery
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from typing import List

from app.database import SessionLocal
from app.models.document import Document

celery = Celery('tasks', broker='redis://redis:6379/0')

class DocumentClassifier:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000)
        self.classifier = MultinomialNB()
        self.labels = ['invoice', 'contract', 'report', 'other']
    
    def train(self, texts: List[str], labels: List[str]):
        X = self.vectorizer.fit_transform(texts)
        self.classifier.fit(X, labels)
    
    def predict(self, text: str) -> str:
        X = self.vectorizer.transform([text])
        return self.classifier.predict(X)[0]

@celery.task
def classify_document(document_id: int):
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document or not document.processed_text:
            return {"error": "Document not found or not processed"}
        
        # Initialize classifier
        classifier = DocumentClassifier()
        
        # In a real application, you would load a pre-trained model
        # For now, we'll return a dummy classification
        document_type = "other"
        
        return {
            "success": True,
            "document_id": document_id,
            "classification": document_type
        }
    except Exception as e:
        return {"error": str(e)}
    finally:
        db.close()
