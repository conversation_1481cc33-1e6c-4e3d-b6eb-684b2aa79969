# Simple Backend Functionality Test
Write-Host "🚀 Testing Backend Functionality..." -ForegroundColor Green

$baseUrl = "http://localhost:8000"

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/health" -Method GET
    Write-Host "✅ Health Check - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Health Check Failed" -ForegroundColor Red
}

# Test 2: Authentication
Write-Host "`n2. Testing Authentication..." -ForegroundColor Yellow
try {
    $loginBody = "username=<EMAIL>&password=testpassword123"
    $response = Invoke-WebRequest -Uri "$baseUrl/api/v1/auth/login" -Method POST -ContentType "application/x-www-form-urlencoded" -Body $loginBody
    $tokenData = $response.Content | ConvertFrom-Json
    $token = $tokenData.access_token
    Write-Host "✅ Authentication - Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Test 3: Document Listing
    Write-Host "`n3. Testing Document Listing..." -ForegroundColor Yellow
    $authHeaders = @{"Authorization" = "Bearer $token"}
    $response = Invoke-WebRequest -Uri "$baseUrl/api/v1/documents/" -Method GET -Headers $authHeaders
    Write-Host "✅ Document Listing - Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Test 4: Analytics
    Write-Host "`n4. Testing Analytics..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "$baseUrl/api/v1/analytics/usage" -Method GET -Headers $authHeaders
    Write-Host "✅ Analytics - Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Test 5: Processing Status
    Write-Host "`n5. Testing Processing Status..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "$baseUrl/api/v1/processing/1/status" -Method GET -Headers $authHeaders
    Write-Host "✅ Processing Status - Status: $($response.StatusCode)" -ForegroundColor Green
    
    Write-Host "`n🎉 All tests passed!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Authentication or subsequent tests failed: $($_.Exception.Message)" -ForegroundColor Red
}
