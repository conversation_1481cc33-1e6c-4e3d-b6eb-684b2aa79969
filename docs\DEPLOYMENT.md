# Deployment Guide

## Prerequisites

- Docker
- Docker Compose
- Domain name (for production)
- SSL certificate (for production)

## Development Deployment

1. Clone the repository
2. Copy `.env.example` to `.env` and configure the variables
3. Run `docker-compose up --build`
4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Production Deployment

1. Set up a server with <PERSON><PERSON> and Docker Compose
2. Clone the repository
3. Copy `.env.example` to `.env` and configure the production variables
4. Configure SSL certificates
5. Run `docker-compose -f docker-compose.prod.yml up --build -d`
6. Set up monitoring and logging

## Environment Variables

Required environment variables:
- `DATABASE_URL`
- `SECRET_KEY`
- `CORS_ORIGINS`
- `UPLOAD_FOLDER`
- `POSTGRES_USER`
- `POSTGRES_PASSWORD`

## Backup and Maintenance

- Database backups are automated using the backup.sh script
- Logs are stored in /var/log/document-processing/
- Monitor system resources and scale as needed

## Troubleshooting

Common issues and solutions:
1. Database connection issues
2. File permission problems
3. Memory limitations
