import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import api from 'services/api';

interface User {
  id: number;
  email: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
}

export const useAuth = () => {
  const navigate = useNavigate();
  const [authState, setAuthState] = useState<AuthState>(() => ({
    user: null,
    token: localStorage.getItem('token')
  }));

  const login = useCallback(async (email: string, password: string) => {
    try {
      const response = await api.post('/auth/login', { username: email, password });
      const { access_token } = response.data;
      
      localStorage.setItem('token', access_token);
      setAuthState({ user: { id: 0, email }, token: access_token });
      
      // Set the token in the API client
      api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      return true;
    } catch (error) {
      throw new Error('Invalid credentials');
    }
  }, []);

  const register = useCallback(async (email: string, password: string) => {
    try {
      await api.post('/auth/register', { username: email, password });
      return true;
    } catch (error) {
      throw new Error('Registration failed');
    }
  }, []);

  const logout = useCallback(() => {
    localStorage.removeItem('token');
    setAuthState({ user: null, token: null });
    delete api.defaults.headers.common['Authorization'];
    navigate('/login');
  }, [navigate]);

  return {
    user: authState.user,
    token: authState.token,
    login,
    register,
    logout
  };
};
