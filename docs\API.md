# API Documentation

## Authentication

### POST /api/auth/login
Login with email and password.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "token_type": "bearer"
}
```

## Documents

### GET /api/documents
List all documents for the authenticated user.

**Response:**
```json
{
    "documents": [
        {
            "id": "123",
            "name": "document.pdf",
            "status": "processed",
            "created_at": "2025-06-02T10:00:00Z"
        }
    ]
}
```

### POST /api/documents/upload
Upload a new document for processing.

**Request:**
- Multipart form data with file

**Response:**
```json
{
    "id": "123",
    "name": "document.pdf",
    "status": "pending"
}
```

## Processing

### POST /api/processing/{document_id}/ocr
Start OCR processing for a document.

### GET /api/processing/{document_id}/status
Get processing status for a document.

## Analytics

### GET /api/analytics/usage
Get usage statistics for the authenticated user.
