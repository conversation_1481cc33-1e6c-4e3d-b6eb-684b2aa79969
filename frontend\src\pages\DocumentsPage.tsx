import React from 'react';
import { Header } from 'components/common/Header';
import { Sidebar } from 'components/common/Sidebar';
import { DocumentList } from 'components/documents/DocumentList';
import { UploadZone } from 'components/documents/UploadZone';

export const DocumentsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <Header title="Documents" />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          <div className="mb-6">
            <UploadZone />
          </div>
          <div className="bg-white rounded-lg shadow">
            <DocumentList />
          </div>
        </main>
      </div>
    </div>
  );
};
