"""fix document status enum to string

Revision ID: 002_fix_document_status
Revises: 001_initial
Create Date: 2025-06-03 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_fix_document_status'
down_revision = '001_initial'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop the enum constraint and change to string
    op.execute("ALTER TABLE documents ALTER COLUMN status TYPE VARCHAR USING status::text")
    op.execute("DROP TYPE IF EXISTS documentstatus")


def downgrade() -> None:
    # Recreate the enum type
    op.execute("CREATE TYPE documentstatus AS ENUM ('pending', 'processing', 'completed', 'failed')")
    op.execute("ALTER TABLE documents ALTER COLUMN status TYPE documentstatus USING status::documentstatus")
