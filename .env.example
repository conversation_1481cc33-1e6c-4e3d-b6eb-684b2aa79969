# Example environment configuration

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME="Document Processing Platform"

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# Database
DATABASE_URL=**************************************/document_processing

# CORS
CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# File Storage
UPLOAD_FOLDER=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=["pdf","png","jpg","jpeg","tiff"]

# PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=document_processing

# Redis
REDIS_URL=redis://redis:6379/0

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
